/**
 * 🗄️ DATABASE RATE LIMITER - EXCEL COPILOT
 * 
 * Rate limiting específico para operações críticas de banco de dados
 * Previne sobrecarga e ataques de força bruta em operações sensíveis
 * 
 * <AUTHOR> Copilot Team
 * @version 1.0.0 - Implementação Inicial (18/06/2025)
 */

import { NextRequest, NextResponse } from 'next/server';

// ============================================================================
// INTERFACES E TIPOS
// ============================================================================

interface DatabaseRateLimitConfig {
  windowMs: number; // Janela de tempo em ms
  maxRequests: number; // Máximo de requests por janela
  operation: string; // Tipo de operação (query, mutation, migration)
  skipSuccessfulRequests?: boolean; // Pular requests bem-sucedidos
  skipFailedRequests?: boolean; // Pular requests falhados
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  firstRequest: number;
}

// ============================================================================
// CONFIGURAÇÕES DE RATE LIMITING POR OPERAÇÃO
// ============================================================================

const DATABASE_RATE_LIMITS: Record<string, DatabaseRateLimitConfig> = {
  // Operações de leitura (queries)
  'db:query': {
    windowMs: 60 * 1000, // 1 minuto
    maxRequests: 100, // 100 queries por minuto
    operation: 'query',
    skipSuccessfulRequests: false,
  },
  
  // Operações de escrita (mutations)
  'db:mutation': {
    windowMs: 60 * 1000, // 1 minuto
    maxRequests: 30, // 30 mutations por minuto
    operation: 'mutation',
    skipSuccessfulRequests: false,
  },
  
  // Operações críticas (migrations, schema changes)
  'db:critical': {
    windowMs: 5 * 60 * 1000, // 5 minutos
    maxRequests: 5, // 5 operações críticas por 5 minutos
    operation: 'critical',
    skipSuccessfulRequests: false,
  },
  
  // Operações de backup
  'db:backup': {
    windowMs: 15 * 60 * 1000, // 15 minutos
    maxRequests: 3, // 3 backups por 15 minutos
    operation: 'backup',
    skipSuccessfulRequests: true, // Não contar backups bem-sucedidos
  },
  
  // Operações de storage
  'db:storage': {
    windowMs: 60 * 1000, // 1 minuto
    maxRequests: 20, // 20 operações de storage por minuto
    operation: 'storage',
    skipSuccessfulRequests: false,
  },
};

// ============================================================================
// STORAGE EM MEMÓRIA PARA RATE LIMITING
// ============================================================================

const rateLimitStore = new Map<string, RateLimitEntry>();

// Limpeza automática de entradas expiradas a cada 5 minutos
setInterval(() => {
  const now = Date.now();
  const entries = Array.from(rateLimitStore.entries());
  for (const [key, entry] of entries) {
    if (now > entry.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 5 * 60 * 1000);

// ============================================================================
// CLASSE PRINCIPAL DO RATE LIMITER
// ============================================================================

export class DatabaseRateLimiter {
  /**
   * Verificar se uma operação está dentro dos limites
   */
  static async checkLimit(
    identifier: string, // IP, userId, ou combinação
    operationType: keyof typeof DATABASE_RATE_LIMITS,
    metadata?: Record<string, unknown>
  ): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
    retryAfter?: number;
  }> {
    const config = DATABASE_RATE_LIMITS[operationType];
    if (!config) {
      throw new Error(`Tipo de operação desconhecido: ${operationType}`);
    }

    const key = `db_rate_limit:${operationType}:${identifier}`;
    const now = Date.now();
    const windowStart = now - config.windowMs;

    let entry = rateLimitStore.get(key);

    // Se não existe entrada ou expirou, criar nova
    if (!entry || entry.resetTime <= now) {
      entry = {
        count: 1,
        resetTime: now + config.windowMs,
        firstRequest: now,
      };
      rateLimitStore.set(key, entry);

      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: entry.resetTime,
      };
    }

    // Verificar se está dentro do limite
    if (entry.count >= config.maxRequests) {
      const retryAfter = Math.ceil((entry.resetTime - now) / 1000);
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter,
      };
    }

    // Incrementar contador
    entry.count++;
    rateLimitStore.set(key, entry);

    return {
      allowed: true,
      remaining: config.maxRequests - entry.count,
      resetTime: entry.resetTime,
    };
  }

  /**
   * Middleware para Next.js API routes
   */
  static middleware(operationType: keyof typeof DATABASE_RATE_LIMITS) {
    return async (req: NextRequest): Promise<NextResponse | null> => {
      try {
        const config = DATABASE_RATE_LIMITS[operationType];
        if (!config) {
          console.error(`Tipo de operação desconhecido: ${operationType}`);
          return null; // Permitir continuar em caso de erro
        }

        // Obter identificador (IP + User-Agent para melhor fingerprinting)
        const ip = req.ip || req.headers.get('x-forwarded-for') || 'unknown';
        const userAgent = req.headers.get('user-agent') || 'unknown';
        const identifier = `${ip}:${userAgent.slice(0, 50)}`; // Limitar tamanho

        const result = await this.checkLimit(identifier, operationType, {
          method: req.method,
          url: req.url,
          timestamp: new Date().toISOString(),
        });

        if (!result.allowed) {
          return NextResponse.json(
            {
              error: 'Rate limit exceeded',
              message: `Too many ${operationType} operations. Try again in ${result.retryAfter} seconds.`,
              retryAfter: result.retryAfter,
            },
            {
              status: 429,
              headers: {
                'X-RateLimit-Limit': config.maxRequests.toString(),
                'X-RateLimit-Remaining': result.remaining.toString(),
                'X-RateLimit-Reset': result.resetTime.toString(),
                'Retry-After': result.retryAfter?.toString() || '60',
              },
            }
          );
        }

        // Adicionar headers informativos
        const response = NextResponse.next();
        response.headers.set('X-RateLimit-Limit', config.maxRequests.toString());
        response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
        response.headers.set('X-RateLimit-Reset', result.resetTime.toString());

        return null; // Permitir continuar
      } catch (error) {
        console.error('Erro no database rate limiter:', error);
        return null; // Em caso de erro, permitir continuar
      }
    };
  }

  /**
   * Obter estatísticas de rate limiting
   */
  static getStats(): {
    totalEntries: number;
    entriesByOperation: Record<string, number>;
    oldestEntry: number | null;
    newestEntry: number | null;
  } {
    const stats = {
      totalEntries: rateLimitStore.size,
      entriesByOperation: {} as Record<string, number>,
      oldestEntry: null as number | null,
      newestEntry: null as number | null,
    };

    const entries = Array.from(rateLimitStore.entries());
    for (const [key, entry] of entries) {
      const operationType = key.split(':')[1];
      if (operationType) {
        stats.entriesByOperation[operationType] = (stats.entriesByOperation[operationType] || 0) + 1;
      }

      if (!stats.oldestEntry || entry.firstRequest < stats.oldestEntry) {
        stats.oldestEntry = entry.firstRequest;
      }
      if (!stats.newestEntry || entry.firstRequest > stats.newestEntry) {
        stats.newestEntry = entry.firstRequest;
      }
    }

    return stats;
  }

  /**
   * Limpar todas as entradas (para testes)
   */
  static clearAll(): void {
    rateLimitStore.clear();
  }
}

// ============================================================================
// EXPORTS E UTILITÁRIOS
// ============================================================================

/**
 * Helper para criar middleware específico
 */
export const createDatabaseRateLimit = (operationType: keyof typeof DATABASE_RATE_LIMITS) => {
  return DatabaseRateLimiter.middleware(operationType);
};

/**
 * Verificar limite para operação específica
 */
export const checkDatabaseRateLimit = DatabaseRateLimiter.checkLimit;

/**
 * Obter configurações de rate limit
 */
export const getDatabaseRateLimitConfig = (operationType: keyof typeof DATABASE_RATE_LIMITS) => {
  return DATABASE_RATE_LIMITS[operationType];
};

/**
 * Export das configurações para uso externo
 */
export { DATABASE_RATE_LIMITS };
