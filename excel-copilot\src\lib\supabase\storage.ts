import { supabaseAdmin } from './client';

/**
 * Configurações dos buckets
 */
export const STORAGE_BUCKETS = {
  EXCEL_FILES: 'excel-files',
  EXPORTS: 'exports',
  TEMPLATES: 'templates',
  BACKUPS: 'backups',
} as const;

/**
 * Tipos para upload de arquivos
 */
export interface FileUploadOptions {
  bucket?: string;
  folder?: string;
  fileName?: string;
  isPublic?: boolean;
  upsert?: boolean;
}

export interface FileUploadResult {
  path: string;
  fullPath: string;
  publicUrl?: string;
  size: number;
}

/**
 * Serviço de Storage do Supabase para arquivos Excel
 */
export class SupabaseStorageService {
  private defaultBucket = STORAGE_BUCKETS.EXCEL_FILES;

  /**
   * Fazer upload de arquivo Excel com validação de permissões
   */
  async uploadExcelFile(
    file: File | Buffer,
    userId: string,
    workbookId: string,
    options: FileUploadOptions = {}
  ): Promise<FileUploadResult> {
    if (!supabaseAdmin) {
      throw new Error('Supabase admin client não está configurado');
    }

    const bucket = options.bucket || this.defaultBucket;
    const folder = options.folder || `users/${userId}/workbooks/${workbookId}`;
    const fileName = options.fileName || `workbook_${Date.now()}.xlsx`;
    const filePath = `${folder}/${fileName}`;

    try {
      // IMPLEMENTAÇÃO CRÍTICA: Validar permissões antes de criar bucket
      const bucketValidation = await this.validateBucketPermissions(bucket, options.isPublic);
      if (!bucketValidation.isValid) {
        throw new Error(`Bucket validation failed: ${bucketValidation.error}`);
      }

      // Garantir que o bucket existe (agora com validação prévia)
      await this.ensureBucketExists(bucket, options.isPublic);

      const { data, error } = await supabaseAdmin.storage.from(bucket).upload(filePath, file, {
        upsert: options.upsert || false,
        contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      if (error) throw error;

      const size = file instanceof File ? file.size : Buffer.byteLength(file);
      const result: FileUploadResult = {
        path: data.path,
        fullPath: data.fullPath,
        size,
      };

      // Se for público, obter URL pública
      if (options.isPublic) {
        const { data: publicData } = supabaseAdmin.storage.from(bucket).getPublicUrl(data.path);
        result.publicUrl = publicData.publicUrl;
      }

      return result;
    } catch (error) {
      throw new Error(
        `Erro no upload: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      );
    }
  }

  /**
   * Fazer download de arquivo Excel
   */
  async downloadExcelFile(filePath: string, bucket: string = this.defaultBucket): Promise<Blob> {
    if (!supabaseAdmin) {
      throw new Error('Supabase admin client não está configurado');
    }

    try {
      const { data, error } = await supabaseAdmin.storage.from(bucket).download(filePath);

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(
        `Erro no download: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      );
    }
  }

  /**
   * Obter URL assinada para download temporário
   */
  async getSignedUrl(
    filePath: string,
    expiresIn: number = 3600, // 1 hora por padrão
    bucket: string = this.defaultBucket
  ): Promise<string> {
    if (!supabaseAdmin) {
      throw new Error('Supabase admin client não está configurado');
    }

    try {
      const { data, error } = await supabaseAdmin.storage
        .from(bucket)
        .createSignedUrl(filePath, expiresIn);

      if (error) throw error;
      return data.signedUrl;
    } catch (error) {
      throw new Error(
        `Erro ao gerar URL: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      );
    }
  }

  /**
   * Deletar arquivo
   */
  async deleteFile(filePath: string, bucket: string = this.defaultBucket): Promise<void> {
    if (!supabaseAdmin) {
      throw new Error('Supabase admin client não está configurado');
    }

    try {
      const { error } = await supabaseAdmin.storage.from(bucket).remove([filePath]);

      if (error) throw error;
    } catch (error) {
      throw new Error(
        `Erro ao deletar: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      );
    }
  }

  /**
   * Listar arquivos de um usuário
   */
  async listUserFiles(
    userId: string,
    bucket: string = this.defaultBucket,
    folder?: string
  ): Promise<Array<{ name: string; size: number; lastModified: string; path: string }>> {
    if (!supabaseAdmin) {
      throw new Error('Supabase admin client não está configurado');
    }

    try {
      const searchPath = folder || `users/${userId}`;

      const { data, error } = await supabaseAdmin.storage.from(bucket).list(searchPath, {
        limit: 100,
        sortBy: { column: 'updated_at', order: 'desc' },
      });

      if (error) throw error;

      return data.map(file => ({
        name: file.name,
        size: file.metadata?.size || 0,
        lastModified: file.updated_at || file.created_at,
        path: `${searchPath}/${file.name}`,
      }));
    } catch (error) {
      throw new Error(
        `Erro ao listar arquivos: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      );
    }
  }

  /**
   * Criar backup de workbook
   */
  async createBackup(
    workbookData: unknown,
    userId: string,
    workbookId: string
  ): Promise<FileUploadResult> {
    const backupData = JSON.stringify({
      workbook: workbookData,
      timestamp: new Date().toISOString(),
      userId,
      workbookId,
    });

    const buffer = Buffer.from(backupData, 'utf-8');
    const fileName = `backup_${workbookId}_${Date.now()}.json`;

    return this.uploadExcelFile(buffer, userId, workbookId, {
      bucket: STORAGE_BUCKETS.BACKUPS,
      fileName,
      folder: `backups/${userId}/${workbookId}`,
    });
  }

  /**
   * IMPLEMENTAÇÃO CRÍTICA: Validar permissões de bucket antes da criação
   */
  private async validateBucketPermissions(
    bucketName: string,
    isPublic: boolean = false
  ): Promise<{ isValid: boolean; error?: string }> {
    if (!supabaseAdmin) {
      return { isValid: false, error: 'Supabase admin client não está configurado' };
    }

    try {
      // Verificar se temos permissões para listar buckets
      const { data: buckets, error: listError } = await supabaseAdmin.storage.listBuckets();

      if (listError) {
        return {
          isValid: false,
          error: `Sem permissão para listar buckets: ${listError.message}`
        };
      }

      // Verificar se o bucket já existe
      const bucketExists = buckets?.some(bucket => bucket.name === bucketName) || false;

      if (bucketExists) {
        // Se existe, verificar se temos permissão de escrita
        try {
          const testPath = `test_permissions_${Date.now()}.txt`;
          const { error: uploadError } = await supabaseAdmin.storage
            .from(bucketName)
            .upload(testPath, 'test', { upsert: true });

          if (uploadError) {
            return {
              isValid: false,
              error: `Sem permissão de escrita no bucket ${bucketName}: ${uploadError.message}`
            };
          }

          // Limpar arquivo de teste
          await supabaseAdmin.storage.from(bucketName).remove([testPath]);

          return { isValid: true };
        } catch (testError) {
          return {
            isValid: false,
            error: `Erro ao testar permissões do bucket: ${testError}`
          };
        }
      } else {
        // Se não existe, verificar se temos permissão para criar
        // Tentativa de criação de bucket temporário para teste
        const testBucketName = `test_permissions_${Date.now()}`;
        const { error: createError } = await supabaseAdmin.storage.createBucket(testBucketName, {
          public: false,
        });

        if (createError) {
          return {
            isValid: false,
            error: `Sem permissão para criar buckets: ${createError.message}`
          };
        }

        // Limpar bucket de teste
        await supabaseAdmin.storage.deleteBucket(testBucketName);

        return { isValid: true };
      }
    } catch (error) {
      return {
        isValid: false,
        error: `Erro na validação de permissões: ${error}`
      };
    }
  }

  /**
   * Garantir que o bucket existe
   */
  private async ensureBucketExists(bucketName: string, isPublic: boolean = false): Promise<void> {
    if (!supabaseAdmin) {
      throw new Error('Supabase admin client não está configurado');
    }

    try {
      const { data: buckets } = await supabaseAdmin.storage.listBuckets();
      const bucketExists = buckets?.some(bucket => bucket.name === bucketName) || false;

      if (!bucketExists) {
        const { error } = await supabaseAdmin.storage.createBucket(bucketName, {
          public: isPublic,
          allowedMimeTypes: [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
            'application/json',
            'text/csv',
          ],
          fileSizeLimit: 50 * 1024 * 1024, // 50MB
        });

        if (error) throw error;
      }
    } catch (error) {
      console.warn(`Aviso: Não foi possível verificar/criar bucket ${bucketName}:`, error);
    }
  }

  /**
   * Obter estatísticas de uso de storage
   */
  async getStorageStats(userId: string): Promise<{
    totalFiles: number;
    totalSize: number;
    bucketStats: Record<string, { files: number; size: number }>;
  }> {
    const stats = {
      totalFiles: 0,
      totalSize: 0,
      bucketStats: {} as Record<string, { files: number; size: number }>,
    };

    try {
      for (const bucket of Object.values(STORAGE_BUCKETS)) {
        const files = await this.listUserFiles(userId, bucket);
        const bucketSize = files.reduce((sum, file) => sum + file.size, 0);

        stats.bucketStats[bucket] = {
          files: files.length,
          size: bucketSize,
        };

        stats.totalFiles += files.length;
        stats.totalSize += bucketSize;
      }
    } catch (error) {
      console.warn('Erro ao obter estatísticas de storage:', error);
    }

    return stats;
  }
}

/**
 * Instância singleton do serviço
 */
export const storageService = new SupabaseStorageService();
