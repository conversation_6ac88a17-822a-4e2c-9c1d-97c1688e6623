import { PrismaClient } from '@prisma/client';

declare global {
  // eslint-disable-next-line no-var
  var __prisma: PrismaClient | undefined;
}

/**
 * 🗄️ PRISMA CLIENT OTIMIZADO - EXCEL COPILOT
 *
 * Cliente Prisma com configurações otimizadas para Supabase/PostgreSQL
 * Inclui connection pooling explícito e configurações de performance
 *
 * @version 2.0.0 - Implementação de Connection Pooling (18/06/2025)
 */

// Configurações de connection pooling baseadas no ambiente
const getConnectionConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isVercel = process.env.VERCEL === '1';

  // Configuração base
  const config: any = {
    // Configurações de logging baseadas no ambiente (simplificadas)
    log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
  };

  // Adicionar configurações de conexão se DATABASE_URL estiver disponível
  if (process.env.DATABASE_URL) {
    config.datasources = {
      db: {
        url: process.env.DATABASE_URL,
      },
    };
  }

  // IMPLEMENTAÇÃO CRÍTICA: Connection pooling explícito para produção
  if (isProduction) {
    config.transactionOptions = {
      maxWait: 5000, // 5 segundos máximo de espera
      timeout: 10000, // 10 segundos timeout
    };
  }

  // Configurações específicas para Vercel
  if (isVercel) {
    config.errorFormat = 'minimal';
  }

  return config;
};

// Prevent multiple instances of Prisma Client in development
const prisma = globalThis.__prisma || new PrismaClient(getConnectionConfig());

// Configurar event listeners para monitoramento de conexão (apenas em desenvolvimento)
if (process.env.NODE_ENV === 'development') {
  try {
    // Event listeners são opcionais e podem não estar disponíveis em todas as versões
    if ('$on' in prisma && typeof prisma.$on === 'function') {
      // @ts-ignore - Event listeners podem variar entre versões do Prisma
      prisma.$on('query', (e: any) => {
        console.log('Query: ' + e.query);
        console.log('Duration: ' + e.duration + 'ms');
      });

      // @ts-ignore - Event listeners podem variar entre versões do Prisma
      prisma.$on('error', (e: any) => {
        console.error('Prisma Error:', e);
      });
    }
  } catch (error) {
    // Ignorar erros de event listeners em desenvolvimento
    console.warn('Event listeners não disponíveis nesta versão do Prisma');
  }
}

// Graceful shutdown
if (typeof process !== 'undefined') {
  process.on('beforeExit', async () => {
    await prisma.$disconnect();
  });
}

if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

export { prisma };
